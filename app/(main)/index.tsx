import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

export default function MainScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <ThemedText type="title" style={styles.welcomeTitle}>
            მთავარი გვერდი
          </ThemedText>
          <ThemedText style={[styles.welcomeSubtitle, { color: colors.text + '80' }]}>
            აირჩიეთ ფუნქცია მარცხენა მენიუდან
          </ThemedText>
        </View>

        {/* Quick Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={[styles.statCard, { backgroundColor: colors.card || colors.background }]}>
            <View style={[styles.statIcon, { backgroundColor: '#4CAF50' }]}>
              <IconSymbol name="person.3.fill" size={24} color="white" />
            </View>
            <View style={styles.statContent}>
              <ThemedText style={[styles.statNumber, { color: colors.text }]}>1,234</ThemedText>
              <ThemedText style={[styles.statLabel, { color: colors.text + '80' }]}>
                მომხმარებლები
              </ThemedText>
            </View>
          </View>

          <View style={[styles.statCard, { backgroundColor: colors.card || colors.background }]}>
            <View style={[styles.statIcon, { backgroundColor: '#2196F3' }]}>
              <IconSymbol name="cube.box.fill" size={24} color="white" />
            </View>
            <View style={styles.statContent}>
              <ThemedText style={[styles.statNumber, { color: colors.text }]}>567</ThemedText>
              <ThemedText style={[styles.statLabel, { color: colors.text + '80' }]}>
                პროდუქტები
              </ThemedText>
            </View>
          </View>

          <View style={[styles.statCard, { backgroundColor: colors.card || colors.background }]}>
            <View style={[styles.statIcon, { backgroundColor: '#FF9800' }]}>
              <IconSymbol name="cart.fill" size={24} color="white" />
            </View>
            <View style={styles.statContent}>
              <ThemedText style={[styles.statNumber, { color: colors.text }]}>89</ThemedText>
              <ThemedText style={[styles.statLabel, { color: colors.text + '80' }]}>
                შეკვეთები
              </ThemedText>
            </View>
          </View>

          <View style={[styles.statCard, { backgroundColor: colors.card || colors.background }]}>
            <View style={[styles.statIcon, { backgroundColor: '#9C27B0' }]}>
              <IconSymbol name="dollarsign.circle.fill" size={24} color="white" />
            </View>
            <View style={styles.statContent}>
              <ThemedText style={[styles.statNumber, { color: colors.text }]}>₾12,345</ThemedText>
              <ThemedText style={[styles.statLabel, { color: colors.text + '80' }]}>
                შემოსავალი
              </ThemedText>
            </View>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.activitySection}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            ბოლო აქტივობა
          </ThemedText>
          
          <View style={[styles.activityCard, { backgroundColor: colors.card || colors.background }]}>
            <View style={styles.activityItem}>
              <View style={[styles.activityIcon, { backgroundColor: '#4CAF50' }]}>
                <IconSymbol name="person.badge.plus" size={16} color="white" />
              </View>
              <View style={styles.activityContent}>
                <ThemedText style={[styles.activityTitle, { color: colors.text }]}>
                  ახალი მომხმარებელი დარეგისტრირდა
                </ThemedText>
                <ThemedText style={[styles.activityTime, { color: colors.text + '60' }]}>
                  5 წუთის წინ
                </ThemedText>
              </View>
            </View>

            <View style={styles.activityItem}>
              <View style={[styles.activityIcon, { backgroundColor: '#2196F3' }]}>
                <IconSymbol name="cart.badge.plus" size={16} color="white" />
              </View>
              <View style={styles.activityContent}>
                <ThemedText style={[styles.activityTitle, { color: colors.text }]}>
                  ახალი შეკვეთა მიღებულია
                </ThemedText>
                <ThemedText style={[styles.activityTime, { color: colors.text + '60' }]}>
                  15 წუთის წინ
                </ThemedText>
              </View>
            </View>

            <View style={styles.activityItem}>
              <View style={[styles.activityIcon, { backgroundColor: '#FF9800' }]}>
                <IconSymbol name="cube.box" size={16} color="white" />
              </View>
              <View style={styles.activityContent}>
                <ThemedText style={[styles.activityTitle, { color: colors.text }]}>
                  პროდუქტი განახლდა
                </ThemedText>
                <ThemedText style={[styles.activityTime, { color: colors.text + '60' }]}>
                  1 საათის წინ
                </ThemedText>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  welcomeSection: {
    marginBottom: 32,
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 32,
  },
  statCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 12,
    flex: 1,
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  statContent: {
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  activitySection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  activityCard: {
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  activityTime: {
    fontSize: 12,
  },
});
