import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import {
    StyleSheet,
    View,
} from 'react-native';

export default function MainScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <ThemedView style={styles.container}>
      {/* Background */}
      <View style={[styles.background, { backgroundColor: colors.backgroundSecondary }]}>
        <View style={[styles.backgroundPattern, { backgroundColor: colors.tint + '08' }]} />
        <View style={[styles.backgroundCircle1, { backgroundColor: colors.accent + '05' }]} />
        <View style={[styles.backgroundCircle2, { backgroundColor: colors.tint + '03' }]} />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.centerContent}>
          <View style={[styles.iconContainer, { backgroundColor: colors.tint }]}>
            <IconSymbol name="house.fill" size={48} color="white" />
          </View>

          <ThemedText type="title" style={styles.welcomeTitle}>
            მთავარი გვერდი
          </ThemedText>

          <ThemedText style={[styles.welcomeSubtitle, { color: colors.icon }]}>
            აირჩიეთ ფუნქცია მარცხენა მენიუდან
          </ThemedText>
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundPattern: {
    position: 'absolute',
    top: -150,
    right: -150,
    width: 400,
    height: 400,
    borderRadius: 200,
    opacity: 0.3,
  },
  backgroundCircle1: {
    position: 'absolute',
    top: 200,
    left: -100,
    width: 300,
    height: 300,
    borderRadius: 150,
    opacity: 0.2,
  },
  backgroundCircle2: {
    position: 'absolute',
    bottom: -100,
    right: -100,
    width: 350,
    height: 350,
    borderRadius: 175,
    opacity: 0.15,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  centerContent: {
    alignItems: 'center',
    maxWidth: 400,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 18,
    textAlign: 'center',
    lineHeight: 24,
  },
});
