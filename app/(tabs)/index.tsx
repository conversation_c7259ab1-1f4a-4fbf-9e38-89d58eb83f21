import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

const { width } = Dimensions.get('window');

interface Function {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'crud' | 'analytics' | 'reports' | 'admin';
}

const functions: Function[] = [
  {
    id: 'users-crud',
    title: 'მომხმარებლების მართვა',
    description: 'მომხმარებლების დამატება, რედაქტირება, წაშლა',
    icon: 'person.3.fill',
    category: 'crud',
  },
  {
    id: 'products-crud',
    title: 'პროდუქტების მართვა',
    description: 'პროდუქტების კატალოგის მართვა',
    icon: 'cube.box.fill',
    category: 'crud',
  },
  {
    id: 'orders-crud',
    title: 'შეკვეთების მართვა',
    description: 'შეკვეთების ნახვა და მართვა',
    icon: 'cart.fill',
    category: 'crud',
  },
  {
    id: 'sales-analytics',
    title: 'გაყიდვების ანალიზი',
    description: 'გაყიდვების სტატისტიკა და ანალიზი',
    icon: 'chart.line.uptrend.xyaxis',
    category: 'analytics',
  },
  {
    id: 'user-analytics',
    title: 'მომხმარებლების ანალიზი',
    description: 'მომხმარებლების აქტივობის ანალიზი',
    icon: 'person.crop.circle.badge.checkmark',
    category: 'analytics',
  },
  {
    id: 'financial-reports',
    title: 'ფინანსური რეპორტები',
    description: 'შემოსავლების და ხარჯების რეპორტები',
    icon: 'dollarsign.circle.fill',
    category: 'reports',
  },
];

export default function HomeScreen() {
  const { user, logout } = useAuth();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [selectedFunction, setSelectedFunction] = useState<Function | null>(null);

  const handleLogout = () => {
    Alert.alert(
      'გასვლა',
      'დარწმუნებული ხართ, რომ გსურთ გასვლა?',
      [
        { text: 'გაუქმება', style: 'cancel' },
        { text: 'გასვლა', style: 'destructive', onPress: logout },
      ]
    );
  };

  const handleFunctionSelect = (func: Function) => {
    setSelectedFunction(func);
    // TODO: Navigate to function page
    Alert.alert(func.title, `${func.description}\n\nეს ფუნქცია მალე იქნება ხელმისაწვდომი.`);
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'crud': return '#4CAF50';
      case 'analytics': return '#2196F3';
      case 'reports': return '#FF9800';
      case 'admin': return '#9C27B0';
      default: return colors.tint;
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.tint }]}>
        <View style={styles.headerContent}>
          <View>
            <ThemedText style={styles.welcomeText}>მოგესალმებით</ThemedText>
            <ThemedText style={styles.userName}>{user?.name}</ThemedText>
          </View>
          <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
            <IconSymbol name="rectangle.portrait.and.arrow.right" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Functions Grid */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ThemedText type="title" style={styles.sectionTitle}>
          ფუნქციები
        </ThemedText>

        <View style={styles.functionsGrid}>
          {functions.map((func) => (
            <TouchableOpacity
              key={func.id}
              style={[
                styles.functionCard,
                { backgroundColor: colors.background },
                selectedFunction?.id === func.id && {
                  borderColor: colors.tint,
                  borderWidth: 2,
                }
              ]}
              onPress={() => handleFunctionSelect(func)}
            >
              <View style={[
                styles.iconContainer,
                { backgroundColor: getCategoryColor(func.category) }
              ]}>
                <IconSymbol name={func.icon} size={32} color="white" />
              </View>

              <ThemedText type="subtitle" style={styles.functionTitle}>
                {func.title}
              </ThemedText>

              <ThemedText style={styles.functionDescription}>
                {func.description}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  userName: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  logoutButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    marginBottom: 20,
    textAlign: 'center',
  },
  functionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  functionCard: {
    width: (width - 60) / 2,
    padding: 20,
    marginBottom: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  functionTitle: {
    textAlign: 'center',
    marginBottom: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  functionDescription: {
    textAlign: 'center',
    fontSize: 12,
    opacity: 0.7,
    lineHeight: 16,
  },
});
